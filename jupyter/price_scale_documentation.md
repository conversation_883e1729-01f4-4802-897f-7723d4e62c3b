# Lightweight Charts - Price Scale Configuration

## Overview
Lightweight Charts menyediakan berbagai cara untuk mengontrol dan menyesuaikan scale harga (price scale) pada chart. Berikut adalah panduan lengkap untuk mengatur scale harga.

## 1. Basic Price Scale Configuration

### `chart.price_scale()` Parameters:

```python
chart.price_scale(
    scale_margin_top=0.1,        # Margin atas (0.0 - 1.0)
    scale_margin_bottom=0.1,     # Margin bawah (0.0 - 1.0)
    border_visible=True,         # Tampilkan border
    border_color='#485c7b',      # Warna border
    text_color='#000000'         # Warna teks
)
```

### Parameter Details:
- **scale_margin_top**: Persentase margin di atas harga tertinggi (0.0 = 0%, 1.0 = 100%)
- **scale_margin_bottom**: Persentase margin di bawah harga terendah
- **border_visible**: Boolean untuk menampilkan/menyembunyikan border
- **border_color**: Warna border dalam format hex, rgb, atau nama warna
- **text_color**: Warna teks label harga

## 2. Visible Range Control

### `chart.set_visible_range()`
Mengatur rentang waktu yang terlihat pada chart:

```python
# Tampilkan 100 candle terakhir
start_time = df['time'].iloc[-100]
end_time = df['time'].iloc[-1]
chart.set_visible_range(start_time, end_time)
```

### `chart.fit()`
Auto-scale untuk menampilkan semua data:

```python
chart.fit()  # Otomatis menyesuaikan scale untuk menampilkan semua data
```

## 3. Advanced Price Scale Features

### Hide/Show Data
```python
chart.hide_data()  # Sembunyikan candles
chart.show_data()  # Tampilkan candles
```

### Price Lines
```python
# Horizontal line pada harga tertentu
chart.horizontal_line(
    price=1.0350,
    color='red',
    width=2,
    style='solid'  # 'solid', 'dashed', 'dotted'
)
```

### Markers
```python
# Marker pada waktu dan harga tertentu
chart.marker(
    time=datetime.now(),
    position='above',     # 'above', 'below', 'inside'
    color='red',
    shape='arrow_down',   # 'arrow_up', 'arrow_down', 'circle', 'square'
    text='Important Level'
)
```

## 4. Time Scale Configuration

### `chart.time_scale()`
```python
chart.time_scale(
    border_visible=True,
    border_color='#485c7b',
    text_color='#ffffff',
    visible=True
)
```

## 5. Complete Styling Example

```python
import pandas as pd
from lightweight_charts import JupyterChart

# Load data
df = pd.read_csv('your_data.csv')

# Create chart
chart = JupyterChart(width=900, height=600)

# Layout styling
chart.layout(
    background_color='#1e1e1e',
    text_color='#ffffff',
    font_size=12,
    font_family='Arial'
)

# Candle styling
chart.candle_style(
    up_color='#26a69a',
    down_color='#ef5350',
    border_up_color='#26a69a',
    border_down_color='#ef5350',
    wick_up_color='#26a69a',
    wick_down_color='#ef5350'
)

# Volume styling
chart.volume_config(
    up_color='rgba(38, 166, 154, 0.5)',
    down_color='rgba(239, 83, 80, 0.5)'
)

# Set data
chart.set(df)

# Price scale configuration
chart.price_scale(
    scale_margin_top=0.15,      # 15% margin atas
    scale_margin_bottom=0.15,   # 15% margin bawah
    border_visible=True,
    border_color='#485c7b',
    text_color='#ffffff'
)

# Time scale configuration
chart.time_scale(
    border_visible=True,
    border_color='#485c7b',
    text_color='#ffffff'
)

# Crosshair styling
chart.crosshair(
    mode='normal',
    vert_color='#758696',
    vert_style='dotted',
    horz_color='#758696',
    horz_style='dotted'
)

# Legend and watermark
chart.legend(visible=True, font_size=12)
chart.watermark('EUR/USD M1', color='rgba(180, 180, 240, 0.3)')

# Display chart
chart.load()
```

## 6. Common Use Cases

### Zoom ke Area Tertentu
```python
# Fokus pada 50 candle terakhir dengan margin besar
start_time = df['time'].iloc[-50]
end_time = df['time'].iloc[-1]
chart.set_visible_range(start_time, end_time)
chart.price_scale(scale_margin_top=0.3, scale_margin_bottom=0.3)
```

### Minimal Margins untuk Maksimal Chart Area
```python
chart.fit()
chart.price_scale(scale_margin_top=0.02, scale_margin_bottom=0.02)
```

### Support/Resistance Lines
```python
high_price = df['high'].max()
low_price = df['low'].min()

chart.horizontal_line(high_price, color='red', width=2)
chart.horizontal_line(low_price, color='green', width=2)
chart.horizontal_line((high_price + low_price) / 2, color='blue', style='dashed')
```

## 7. Tips dan Best Practices

1. **Margin Values**: Gunakan nilai 0.1-0.2 (10-20%) untuk margin yang seimbang
2. **Color Consistency**: Pastikan warna konsisten dengan tema chart
3. **Performance**: Gunakan `fit()` setelah set data untuk performa optimal
4. **Responsive**: Sesuaikan ukuran chart dengan container
5. **Accessibility**: Gunakan kontras warna yang baik untuk readability

## 8. Troubleshooting

### Chart Tidak Muncul
- Pastikan data format benar (time, open, high, low, close, volume)
- Gunakan `JupyterChart` untuk Jupyter Notebook
- Restart kernel jika ada error "process twice"

### Scale Tidak Sesuai
- Gunakan `chart.fit()` untuk auto-scale
- Periksa nilai margin (harus 0.0-1.0)
- Pastikan data tidak mengandung NaN values

### Performance Issues
- Batasi jumlah data yang ditampilkan
- Gunakan `set_visible_range()` untuk fokus pada area tertentu
- Hindari terlalu banyak markers/lines
