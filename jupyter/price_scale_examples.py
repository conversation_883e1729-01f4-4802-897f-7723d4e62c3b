# CONTOH LENGKAP: Mengatur Scale Harga di Lightweight Charts
import pandas as pd
from lightweight_charts import JupyterChart

def load_and_format_data():
    """Load dan format data EUR/USD"""
    df = pd.read_csv('../histdata/MT/M1/eurusd/2025/1/DAT_MT_EURUSD_M1_202501.csv',
                     names=['date', 'time', 'open', 'high', 'low', 'close', 'volume'])
    
    df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
    df_formatted = df[['datetime', 'open', 'high', 'low', 'close', 'volume']].copy()
    df_formatted.rename(columns={'datetime': 'time'}, inplace=True)
    
    return df_formatted

# ============================================================================
# 1. BASIC PRICE SCALE CONFIGURATION
# ============================================================================
def example_basic_price_scale():
    """Contoh pengaturan price scale dasar"""
    print("=== BASIC PRICE SCALE ===")
    
    df = load_and_format_data()
    chart = JupyterChart(width=900, height=600)
    
    chart.set(df)
    
    # Pengaturan price scale dasar
    chart.price_scale(
        scale_margin_top=0.1,      # 10% margin atas
        scale_margin_bottom=0.1,   # 10% margin bawah
        border_visible=True,       # Tampilkan border
        border_color='#485c7b',    # Warna border
        text_color='#000000'       # Warna teks
    )
    
    print(f"Price range: {df['close'].min():.5f} - {df['close'].max():.5f}")
    chart.load()
    return chart

# ============================================================================
# 2. VISIBLE RANGE CONTROL
# ============================================================================
def example_visible_range():
    """Contoh mengatur visible range (zoom ke area tertentu)"""
    print("=== VISIBLE RANGE CONTROL ===")
    
    df = load_and_format_data()
    chart = JupyterChart(width=900, height=600)
    
    chart.set(df)
    
    # Tampilkan hanya 50 candle terakhir
    start_time = df['time'].iloc[-50]
    end_time = df['time'].iloc[-1]
    chart.set_visible_range(start_time, end_time)
    
    # Price scale dengan margin yang lebih besar
    chart.price_scale(
        scale_margin_top=0.3,      # 30% margin atas
        scale_margin_bottom=0.3    # 30% margin bawah
    )
    
    print(f"Showing last 50 candles from {start_time} to {end_time}")
    chart.load()
    return chart

# ============================================================================
# 3. AUTO-FIT DATA
# ============================================================================
def example_auto_fit():
    """Contoh auto-fit untuk menampilkan semua data"""
    print("=== AUTO-FIT DATA ===")
    
    df = load_and_format_data()
    chart = JupyterChart(width=900, height=600)
    
    chart.set(df)
    
    # Auto-fit semua data
    chart.fit()
    
    # Minimal margin untuk memaksimalkan area chart
    chart.price_scale(
        scale_margin_top=0.05,     # 5% margin atas
        scale_margin_bottom=0.05   # 5% margin bawah
    )
    
    print("Auto-fitted all data with minimal margins")
    chart.load()
    return chart

# ============================================================================
# 4. CUSTOM STYLING WITH PRICE SCALE
# ============================================================================
def example_custom_styling():
    """Contoh styling lengkap dengan price scale"""
    print("=== CUSTOM STYLING ===")
    
    df = load_and_format_data()
    chart = JupyterChart(width=900, height=600)
    
    # Dark theme layout
    chart.layout(
        background_color='#1e1e1e',
        text_color='#ffffff',
        font_size=12,
        font_family='Arial'
    )
    
    # Custom candle colors
    chart.candle_style(
        up_color='#26a69a',
        down_color='#ef5350',
        border_up_color='#26a69a',
        border_down_color='#ef5350',
        wick_up_color='#26a69a',
        wick_down_color='#ef5350'
    )
    
    # Volume styling
    chart.volume_config(
        up_color='rgba(38, 166, 154, 0.5)',
        down_color='rgba(239, 83, 80, 0.5)'
    )
    
    chart.set(df)
    
    # Price scale dengan styling
    chart.price_scale(
        scale_margin_top=0.15,
        scale_margin_bottom=0.15,
        border_visible=True,
        border_color='#485c7b',
        text_color='#ffffff'
    )
    
    # Time scale styling
    chart.time_scale(
        border_visible=True,
        border_color='#485c7b',
        text_color='#ffffff'
    )
    
    # Crosshair styling
    chart.crosshair(
        mode='normal',
        vert_color='#758696',
        vert_style='dotted',
        horz_color='#758696',
        horz_style='dotted'
    )
    
    # Legend dan watermark
    chart.legend(visible=True, font_size=12)
    chart.watermark('EUR/USD M1', color='rgba(180, 180, 240, 0.3)')
    
    print("Custom dark theme with styled price scale")
    chart.load()
    return chart

# ============================================================================
# 5. PRICE LINES AND MARKERS
# ============================================================================
def example_price_lines():
    """Contoh menambahkan price lines dan markers"""
    print("=== PRICE LINES & MARKERS ===")
    
    df = load_and_format_data()
    chart = JupyterChart(width=900, height=600)
    
    chart.set(df)
    
    # Hitung support dan resistance levels
    high_price = df['high'].max()
    low_price = df['low'].min()
    mid_price = (high_price + low_price) / 2
    
    # Tambahkan horizontal lines
    chart.horizontal_line(high_price, color='red', width=2, style='solid')
    chart.horizontal_line(low_price, color='green', width=2, style='solid')
    chart.horizontal_line(mid_price, color='blue', width=1, style='dashed')
    
    # Tambahkan marker di titik tertinggi dan terendah
    high_idx = df['high'].idxmax()
    low_idx = df['low'].idxmin()
    
    chart.marker(time=df.iloc[high_idx]['time'], position='above', 
                color='red', shape='arrow_down', text=f'High: {high_price:.5f}')
    chart.marker(time=df.iloc[low_idx]['time'], position='below', 
                color='green', shape='arrow_up', text=f'Low: {low_price:.5f}')
    
    # Price scale dengan margin untuk menampilkan lines
    chart.price_scale(
        scale_margin_top=0.2,
        scale_margin_bottom=0.2
    )
    
    print(f"Added price lines: High={high_price:.5f}, Low={low_price:.5f}, Mid={mid_price:.5f}")
    chart.load()
    return chart

# ============================================================================
# MAIN EXECUTION
# ============================================================================
if __name__ == "__main__":
    print("Lightweight Charts - Price Scale Examples")
    print("=" * 50)
    
    # Uncomment untuk menjalankan contoh yang diinginkan:
    
    # example_basic_price_scale()
    # example_visible_range()
    # example_auto_fit()
    # example_custom_styling()
    # example_price_lines()
    
    print("\nUntuk menjalankan contoh, uncomment fungsi yang diinginkan di bagian main.")
