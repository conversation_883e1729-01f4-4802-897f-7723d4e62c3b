# Price Precision Guide - Lightweight Charts

## Overview
Panduan lengkap untuk mengatur precision (jumlah digit desimal) pada price scale di Lightweight Charts, khususnya untuk trading forex dan instrumen finansial lainnya.

## 1. <PERSON><PERSON><PERSON> Dasar Price Precision

### Apa itu Price Precision?
Price precision menentukan berapa banyak digit desimal yang ditampilkan pada price scale:

- **2 Digit**: 1.04, 1.05, 1.06 (tidak cocok untuk forex)
- **4 Digit**: 1.0400, 1.0401, 1.0402 (standard forex)
- **5 Digit**: 1.04000, 1.04001, 1.04002 (broker 5-digit)

### Pip Values untuk Forex:
- **4-digit broker**: 1 pip = 0.0001
- **5-digit broker**: 1 pip = 0.00001 (pipette)

## 2. Implementasi di Lightweight Charts

### Basic 4-Digit Precision Setup:
```python
import pandas as pd
from lightweight_charts import Jupyter<PERSON>hart

# Load data
df = pd.read_csv('eurusd_data.csv')

# Create chart
chart = JupyterChart(width=900, height=600)

# Set data
chart.set(df)

# Configure price scale for 4-digit precision
chart.price_scale(
    scale_margin_top=0.05,      # 5% margin atas
    scale_margin_bottom=0.05,   # 5% margin bawah
    border_visible=True,
    border_color='#2a2e39',
    text_color='#d1d4dc'
)

# Display
chart.load()
```

## 3. Forex-Specific Configurations

### EUR/USD 4-Digit Configuration:
```python
def setup_eurusd_4digit(chart, df):
    # Professional forex styling
    chart.layout(
        background_color='#0d1421',
        text_color='#d1d4dc',
        font_size=11,
        font_family='Consolas, Monaco, monospace'  # Monospace untuk precision
    )
    
    # Forex candle colors
    chart.candle_style(
        up_color='#089981',      # Hijau untuk bullish
        down_color='#f23645',    # Merah untuk bearish
        border_up_color='#089981',
        border_down_color='#f23645',
        wick_up_color='#089981',
        wick_down_color='#f23645'
    )
    
    # Price scale untuk 4-digit
    chart.price_scale(
        scale_margin_top=0.05,
        scale_margin_bottom=0.05,
        border_visible=True,
        border_color='#2a2e39',
        text_color='#d1d4dc'
    )
    
    return chart
```

### 5-Digit Broker Configuration:
```python
def setup_5digit_precision(chart, df):
    # Tighter margins untuk 5-digit precision
    chart.price_scale(
        scale_margin_top=0.03,
        scale_margin_bottom=0.03,
        border_visible=True,
        border_color='#363a45',
        text_color='#d1d4dc'
    )
    
    return chart
```

## 4. Grid Lines dan Level Markers

### Grid Lines Setiap 10 Pips (4-digit):
```python
def add_pip_grid_4digit(chart, current_price):
    base_price = round(current_price, 3)  # Round ke 3 digit
    
    for i in range(-5, 6):  # -50 pips to +50 pips
        level = base_price + (i * 0.0010)  # 10 pips = 0.0010
        color = '#4a90e2' if i == 0 else '#2a2e39'
        width = 2 if i == 0 else 1
        style = 'solid' if i == 0 else 'dotted'
        
        chart.horizontal_line(level, color=color, width=width, style=style)
```

### Grid Lines Setiap 1 Pip (5-digit):
```python
def add_pip_grid_5digit(chart, current_price):
    base_price = round(current_price, 4)  # Round ke 4 digit
    
    for i in range(-10, 11):  # -10 pips to +10 pips
        level = base_price + (i * 0.00010)  # 1 pip = 0.00010 (5-digit)
        color = '#4a90e2' if i == 0 else '#363a45'
        width = 2 if i == 0 else 1
        
        chart.horizontal_line(level, color=color, width=width, style='dotted')
```

## 5. Support/Resistance Levels

### Psychological Levels (50 pips intervals):
```python
def add_psychological_levels(chart, current_price):
    base = round(current_price, 2)  # Round ke 2 digit
    
    for i in range(-3, 4):
        level = base + (i * 0.0050)  # 50 pips intervals
        
        if level > current_price:
            # Resistance (red)
            chart.horizontal_line(level, color='#ff6b6b', width=1, style='dashed')
        elif level < current_price:
            # Support (green)
            chart.horizontal_line(level, color='#00d4aa', width=1, style='dashed')
```

## 6. Pip Calculations

### Utility Functions:
```python
def calculate_pips_4digit(price1, price2):
    """Calculate pips difference for 4-digit broker"""
    return abs(price1 - price2) * 10000

def calculate_pips_5digit(price1, price2):
    """Calculate pips difference for 5-digit broker"""
    return abs(price1 - price2) * 100000

def format_price_4digit(price):
    """Format price with 4 decimal places"""
    return f"{price:.4f}"

def format_price_5digit(price):
    """Format price with 5 decimal places"""
    return f"{price:.5f}"

# Example usage:
current = 1.04567
high = 1.04789

pips_4digit = calculate_pips_4digit(current, high)
pips_5digit = calculate_pips_5digit(current, high)

print(f"4-digit: {pips_4digit:.1f} pips")
print(f"5-digit: {pips_5digit:.1f} pips")
```

## 7. Complete Example

### Professional Forex Chart Setup:
```python
import pandas as pd
from lightweight_charts import JupyterChart

def create_professional_forex_chart(df):
    chart = JupyterChart(width=1000, height=700)
    
    # Professional theme
    chart.layout(
        background_color='#1e222d',
        text_color='#d1d4dc',
        font_size=12,
        font_family='JetBrains Mono, Consolas, monospace'
    )
    
    # Forex colors
    chart.candle_style(
        up_color='#00d4aa',
        down_color='#ff6b6b',
        border_up_color='#00d4aa',
        border_down_color='#ff6b6b',
        wick_up_color='#00d4aa',
        wick_down_color='#ff6b6b'
    )
    
    # Volume
    chart.volume_config(
        up_color='rgba(0, 212, 170, 0.3)',
        down_color='rgba(255, 107, 107, 0.3)'
    )
    
    # Set data
    chart.set(df)
    
    # 4-digit precision
    chart.price_scale(
        scale_margin_top=0.08,
        scale_margin_bottom=0.08,
        border_visible=True,
        border_color='#363a45',
        text_color='#d1d4dc'
    )
    
    # Add current price line
    current_price = df['close'].iloc[-1]
    chart.horizontal_line(current_price, color='#4a90e2', width=2, style='solid')
    
    # Add price marker
    chart.marker(
        time=df.iloc[-1]['time'],
        position='inside',
        color='#4a90e2',
        shape='circle',
        text=f'{current_price:.4f}'
    )
    
    # Styling
    chart.crosshair(
        mode='normal',
        vert_color='#4a90e2',
        vert_style='dashed',
        horz_color='#4a90e2',
        horz_style='dashed'
    )
    
    chart.legend(visible=True, font_size=11)
    chart.watermark('EUR/USD - 4 Digit Precision', color='rgba(74, 144, 226, 0.1)')
    
    return chart

# Usage:
df = load_forex_data()
chart = create_professional_forex_chart(df)
chart.load()
```

## 8. Best Practices

### Font Selection:
- **Monospace fonts** untuk precision: `Consolas`, `Monaco`, `JetBrains Mono`
- **Font size**: 10-12px untuk readability
- **Color contrast**: Pastikan teks terlihat jelas

### Color Scheme:
- **Background**: Dark theme (`#1e222d`, `#0d1421`)
- **Text**: Light gray (`#d1d4dc`)
- **Grid**: Subtle gray (`#2a2e39`, `#363a45`)
- **Current price**: Blue (`#4a90e2`)

### Performance:
- Batasi jumlah grid lines (max 20-30)
- Gunakan subset data untuk chart yang responsive
- Update precision sesuai instrumen trading

## 9. Troubleshooting

### Precision Tidak Muncul:
- Pastikan data memiliki precision yang cukup
- Check format data (harus float, bukan string)
- Gunakan font monospace untuk alignment yang baik

### Grid Lines Terlalu Padat:
- Kurangi range grid lines
- Increase interval (10 pips instead of 1 pip)
- Gunakan margin yang lebih besar

### Performance Issues:
- Batasi jumlah horizontal lines
- Gunakan data subset untuk testing
- Optimize update frequency untuk real-time data
