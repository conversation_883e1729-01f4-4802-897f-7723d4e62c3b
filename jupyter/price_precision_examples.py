# CONTOH LENGKAP: Mengatur Price Precision di Lightweight Charts
import pandas as pd
from lightweight_charts import Ju<PERSON><PERSON><PERSON>hart

def load_forex_data():
    """Load EUR/USD data"""
    df = pd.read_csv('../histdata/MT/M1/eurusd/2025/1/DAT_MT_EURUSD_M1_202501.csv',
                     names=['date', 'time', 'open', 'high', 'low', 'close', 'volume'])
    
    df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
    df_formatted = df[['datetime', 'open', 'high', 'low', 'close', 'volume']].copy()
    df_formatted.rename(columns={'datetime': 'time'}, inplace=True)
    
    return df_formatted

# ============================================================================
# 1. FOREX PRECISION (4-5 DIGIT DESIMAL)
# ============================================================================
def forex_4_digit_precision():
    """EUR/USD dengan 4 digit precision (1.0400, 1.0401, 1.0402)"""
    print("=== FOREX 4 DIGIT PRECISION ===")
    
    df = load_forex_data()
    subset = df.iloc[-100:].copy()  # 100 candle terakhir
    
    chart = JupyterChart(width=900, height=600)
    
    # Forex styling
    chart.layout(
        background_color='#0d1421',
        text_color='#d1d4dc',
        font_size=11,
        font_family='Consolas, Monaco, monospace'  # Monospace untuk precision
    )
    
    chart.candle_style(
        up_color='#089981',      # Hijau
        down_color='#f23645',    # Merah
        border_up_color='#089981',
        border_down_color='#f23645',
        wick_up_color='#089981',
        wick_down_color='#f23645'
    )
    
    chart.set(subset)
    
    # KUNCI: Price scale untuk 4 digit precision
    chart.price_scale(
        scale_margin_top=0.05,
        scale_margin_bottom=0.05,
        border_visible=True,
        border_color='#2a2e39',
        text_color='#d1d4dc'
    )
    
    # Grid lines setiap 10 pips (0.0010)
    current_price = subset['close'].iloc[-1]
    base_price = round(current_price, 3)  # Base ke 3 digit
    
    print(f"Current price: {current_price:.4f}")
    print("Adding grid lines every 10 pips...")
    
    for i in range(-5, 6):  # -50 pips to +50 pips
        level = base_price + (i * 0.0010)  # 10 pips = 0.0010
        color = '#4a90e2' if i == 0 else '#2a2e39'
        width = 2 if i == 0 else 1
        style = 'solid' if i == 0 else 'dotted'
        
        chart.horizontal_line(level, color=color, width=width, style=style)
        print(f"  Level {i:+2d}: {level:.4f}")
    
    chart.watermark('EUR/USD - 4 Digit Precision', color='rgba(180, 180, 240, 0.2)')
    chart.load()
    return chart

# ============================================================================
# 2. FOREX PRECISION (5 DIGIT DESIMAL) - UNTUK BROKER 5 DIGIT
# ============================================================================
def forex_5_digit_precision():
    """EUR/USD dengan 5 digit precision (1.04000, 1.04001, 1.04002)"""
    print("=== FOREX 5 DIGIT PRECISION ===")
    
    df = load_forex_data()
    subset = df.iloc[-100:].copy()
    
    chart = JupyterChart(width=900, height=600)
    
    # Dark professional theme
    chart.layout(
        background_color='#131722',
        text_color='#d1d4dc',
        font_size=10,
        font_family='SF Mono, Consolas, monospace'
    )
    
    chart.candle_style(
        up_color='#26a69a',
        down_color='#ef5350',
        border_up_color='#26a69a',
        border_down_color='#ef5350',
        wick_up_color='#26a69a',
        wick_down_color='#ef5350'
    )
    
    chart.set(subset)
    
    # 5 digit precision
    chart.price_scale(
        scale_margin_top=0.03,
        scale_margin_bottom=0.03,
        border_visible=True,
        border_color='#363a45',
        text_color='#d1d4dc'
    )
    
    # Grid lines setiap 1 pip (0.00010) untuk 5 digit
    current_price = subset['close'].iloc[-1]
    base_price = round(current_price, 4)  # Base ke 4 digit
    
    print(f"Current price: {current_price:.5f}")
    print("Adding grid lines every 1 pip (5-digit)...")
    
    for i in range(-10, 11):  # -10 pips to +10 pips
        level = base_price + (i * 0.00010)  # 1 pip = 0.00010 (5 digit)
        color = '#4a90e2' if i == 0 else '#363a45'
        width = 2 if i == 0 else 1
        style = 'solid' if i == 0 else 'dotted'
        
        chart.horizontal_line(level, color=color, width=width, style=style)
        if i % 5 == 0:  # Print every 5 pips
            print(f"  Level {i:+2d}: {level:.5f}")
    
    chart.watermark('EUR/USD - 5 Digit Precision', color='rgba(180, 180, 240, 0.15)')
    chart.load()
    return chart

# ============================================================================
# 3. CUSTOM PRECISION DENGAN PIP CALCULATOR
# ============================================================================
def custom_precision_with_pips():
    """Custom precision dengan pip calculator dan level markers"""
    print("=== CUSTOM PRECISION WITH PIP CALCULATOR ===")
    
    df = load_forex_data()
    subset = df.iloc[-150:].copy()
    
    chart = JupyterChart(width=1000, height=700)
    
    # Professional trading theme
    chart.layout(
        background_color='#1e222d',
        text_color='#d1d4dc',
        font_size=12,
        font_family='JetBrains Mono, Consolas, monospace'
    )
    
    chart.candle_style(
        up_color='#00d4aa',
        down_color='#ff6b6b',
        border_up_color='#00d4aa',
        border_down_color='#ff6b6b',
        wick_up_color='#00d4aa',
        wick_down_color='#ff6b6b'
    )
    
    chart.volume_config(
        up_color='rgba(0, 212, 170, 0.3)',
        down_color='rgba(255, 107, 107, 0.3)'
    )
    
    chart.set(subset)
    
    # High precision scale
    chart.price_scale(
        scale_margin_top=0.08,
        scale_margin_bottom=0.08,
        border_visible=True,
        border_color='#363a45',
        text_color='#d1d4dc'
    )
    
    # Calculate key levels
    current_price = subset['close'].iloc[-1]
    high_price = subset['high'].max()
    low_price = subset['low'].min()
    
    # Support/Resistance levels dengan precision
    resistance_levels = []
    support_levels = []
    
    # Round ke level psikologis (setiap 50 pips)
    base = round(current_price, 2)  # Round ke 2 digit
    
    for i in range(-3, 4):
        level = base + (i * 0.0050)  # 50 pips intervals
        if level > current_price:
            resistance_levels.append(level)
        elif level < current_price:
            support_levels.append(level)
    
    # Add resistance lines (red)
    for level in resistance_levels:
        chart.horizontal_line(level, color='#ff6b6b', width=1, style='dashed')
        print(f"Resistance: {level:.4f}")
    
    # Add support lines (green)
    for level in support_levels:
        chart.horizontal_line(level, color='#00d4aa', width=1, style='dashed')
        print(f"Support: {level:.4f}")
    
    # Current price line (blue)
    chart.horizontal_line(current_price, color='#4a90e2', width=2, style='solid')
    
    # Add markers for key levels
    chart.marker(
        time=subset.iloc[-1]['time'],
        position='inside',
        color='#4a90e2',
        shape='circle',
        text=f'{current_price:.4f}'
    )
    
    # Pip calculation
    range_pips = (high_price - low_price) * 10000
    current_from_high_pips = (high_price - current_price) * 10000
    current_from_low_pips = (current_price - low_price) * 10000
    
    print(f"\n=== PIP ANALYSIS ===")
    print(f"Current: {current_price:.4f}")
    print(f"High: {high_price:.4f}")
    print(f"Low: {low_price:.4f}")
    print(f"Range: {range_pips:.1f} pips")
    print(f"From High: {current_from_high_pips:.1f} pips")
    print(f"From Low: {current_from_low_pips:.1f} pips")
    
    # Styling
    chart.crosshair(
        mode='normal',
        vert_color='#4a90e2',
        vert_style='dashed',
        horz_color='#4a90e2',
        horz_style='dashed'
    )
    
    chart.legend(visible=True, font_size=11)
    chart.watermark('EUR/USD - Professional Precision', color='rgba(74, 144, 226, 0.1)')
    
    chart.load()
    return chart

# ============================================================================
# 4. COMPARISON: 2 vs 4 vs 5 DIGIT PRECISION
# ============================================================================
def precision_comparison():
    """Perbandingan precision 2, 4, dan 5 digit"""
    print("=== PRECISION COMPARISON ===")
    
    df = load_forex_data()
    current_price = df['close'].iloc[-1]
    
    print(f"Original price: {current_price}")
    print(f"2 Digit: {current_price:.2f}")
    print(f"4 Digit: {current_price:.4f}")
    print(f"5 Digit: {current_price:.5f}")
    
    print(f"\nPrice movements:")
    print(f"1 pip (4-digit): {current_price:.4f} -> {current_price + 0.0001:.4f}")
    print(f"1 pip (5-digit): {current_price:.5f} -> {current_price + 0.00001:.5f}")
    print(f"10 pips (4-digit): {current_price:.4f} -> {current_price + 0.0010:.4f}")

# ============================================================================
# MAIN EXECUTION
# ============================================================================
if __name__ == "__main__":
    print("Lightweight Charts - Price Precision Examples")
    print("=" * 60)
    
    # Uncomment untuk menjalankan contoh yang diinginkan:
    
    # forex_4_digit_precision()
    # forex_5_digit_precision()
    # custom_precision_with_pips()
    # precision_comparison()
    
    print("\nUntuk menjalankan contoh, uncomment fungsi yang diinginkan.")
